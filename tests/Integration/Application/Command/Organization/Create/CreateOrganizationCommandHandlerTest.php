<?php

declare(strict_types=1);

namespace App\Tests\Integration\Application\Command\Shop\Create;

use App\Application\Command\Organization\Create\CreateOrganizationCommandHandler;
use App\Tests\Integration\IntegrationTestCase;

final class CreateOrganizationCommandHandlerTest extends IntegrationTestCase
{
    private CreateOrganizationCommandHandler $handler;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var CreateOrganizationCommandHandler $handler */
        $handler = self::getContainer()->get(CreateOrganizationCommandHandler::class);
        $this->handler = $handler;
    }

    public function testCreateOrganizationWithCorrectDataReturnResult(): void
    {

    }

    public function testCreateOrganizationWithIncorrectDataReturnError(): void
    {

    }
}
