<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1\Organization;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Shared\Factory\OrganizationFactory;
use App\Tests\Shared\Random\Generator;
use Exception;

final class UpdateOrganizationActionTest extends FunctionalTestCase
{
    protected const string ROUTE = '/tenants/v1/{_locale}/organizations/{id}';
    protected const string METHOD = 'PUT';
    protected const string LOCALE = 'en';

    public function testUpdateOrganizationWorks(): void
    {
        // First create an organization to update
        $createData = [
            'name' => 'Organization to Update',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'address' => '123 Update Street'
        ];
        
        $createRoute = str_replace('{_locale}', self::LOCALE, '/tenants/v1/{_locale}/organizations');
        $this->client->jsonRequest('POST', $createRoute, $createData);
        $this->assertResponseStatusCodeSame(201);
        $createResponse = $this->getDecodedJsonResponse();
        $organizationId = $createResponse['data']['id'];

        // Now update the organization
        $updateData = [
            'name' => 'Updated Organization Name',
            'email' => '<EMAIL>',
            'phone' => '******-9999',
            'address' => '456 Updated Street'
        ];

        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $organizationId], self::ROUTE);
        $this->client->jsonRequest(self::METHOD, $route, $updateData);
        $this->assertResponseStatusCodeSame(200);

        $response = $this->getDecodedJsonResponse();
        $this->assertArrayHasKey('data', $response);
        $this->assertEquals($organizationId, $response['data']['id']);
        $this->assertEquals($updateData['name'], $response['data']['name']);
        $this->assertEquals($updateData['email'], $response['data']['email']);
        $this->assertEquals($updateData['phone'], $response['data']['phone']);
        $this->assertEquals($updateData['address'], $response['data']['address']);
    }

    public function testUpdateOrganizationFailsWithExistingName(): void
    {
        // Try to update to an existing organization name
        $createData = [
            'name' => 'Organization to Update 2',
            'email' => '<EMAIL>',
            'phone' => '******-0124',
            'address' => '124 Update Street'
        ];
        
        $createRoute = str_replace('{_locale}', self::LOCALE, '/tenants/v1/{_locale}/organizations');
        $this->client->jsonRequest('POST', $createRoute, $createData);
        $this->assertResponseStatusCodeSame(201);
        $createResponse = $this->getDecodedJsonResponse();
        $organizationId = $createResponse['data']['id'];

        $updateData = [
            'name' => OrganizationFactory::NAME, // This name already exists in fixtures
            'email' => '<EMAIL>',
            'phone' => '******-9999',
            'address' => '456 Updated Street'
        ];

        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $organizationId], self::ROUTE);
        $this->client->jsonRequest(self::METHOD, $route, $updateData);
        $this->assertResponseStatusCodeSame(422);

        $expectedResponse = [
            'errors' => [
                'name' => sprintf('Name %s already exists', OrganizationFactory::NAME),
            ],
        ];
        $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());
    }

    public function testUpdateOrganizationFailsWithNotFound(): void
    {
        $updateData = [
            'name' => 'Updated Organization Name',
            'email' => '<EMAIL>',
            'phone' => '******-9999',
            'address' => '456 Updated Street'
        ];

        $nonExistentId = '550e8400-e29b-41d4-a716-446655440000';
        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $nonExistentId], self::ROUTE);
        $this->client->jsonRequest(self::METHOD, $route, $updateData);
        $this->assertResponseStatusCodeSame(404);
    }

    /**
     * @dataProvider provideUpdateOrganizationInvalidData
     */
    public function testUpdateOrganizationFailsWithInvalidData(array $data, array $errors): void
    {
        // First create an organization to update
        $createData = [
            'name' => 'Organization to Update 3',
            'email' => '<EMAIL>',
            'phone' => '******-0125',
            'address' => '125 Update Street'
        ];
        
        $createRoute = str_replace('{_locale}', self::LOCALE, '/tenants/v1/{_locale}/organizations');
        $this->client->jsonRequest('POST', $createRoute, $createData);
        $this->assertResponseStatusCodeSame(201);
        $createResponse = $this->getDecodedJsonResponse();
        $organizationId = $createResponse['data']['id'];

        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $organizationId], self::ROUTE);
        $this->client->jsonRequest(self::METHOD, $route, $data);

        $this->assertResponseStatusCodeSame(400);
        $expectedResponse = ['errors' => $errors];
        $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());
    }

    /**
     * @throws Exception
     */
    public function provideUpdateOrganizationInvalidData(): array
    {
        return [
            'emptyName' => [
                ['name' => '', 'email' => '<EMAIL>', 'address' => 'Valid Address'],
                ['name' => 'This value should not be blank.'],
            ],
            'emptyEmail' => [
                ['name' => 'Valid Name', 'email' => '', 'address' => 'Valid Address'],
                ['email' => 'This value should not be blank.'],
            ],
            'invalidEmail' => [
                ['name' => 'Valid Name', 'email' => 'invalid-email', 'address' => 'Valid Address'],
                ['email' => 'This value is not a valid email address.'],
            ],
            'emptyAddress' => [
                ['name' => 'Valid Name', 'email' => '<EMAIL>', 'address' => ''],
                ['address' => 'This value should not be blank.'],
            ],
            'longName' => [
                ['name' => Generator::string(101), 'email' => '<EMAIL>', 'address' => 'Valid Address'],
                ['name' => 'This value is too long. It should have 100 characters or less.'],
            ],
        ];
    }
}
