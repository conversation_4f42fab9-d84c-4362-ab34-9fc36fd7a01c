<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1\Organization;

use App\Tests\Functional\Infrastructure\Delivery\Api\V1\ApiV1TestCase;

final class OrganizationCrudTest extends ApiV1TestCase
{
    protected const string ROUTE = '/{_locale}/organizations';
    protected const string METHOD = 'POST';

    public function testCreateOrganization(): string
    {
        $organizationData = [
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'address' => '123 Test Street, Test City, TC 12345'
        ];

        $this->makeRequest(pathParams: ['_locale' => self::LOCALE], data: $organizationData);

        $this->assertResponseStatusCodeSame(201);
        $response = $this->getDecodedJsonResponse();

        // Debug: print the actual response
        var_dump($response);

        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('id', $response['data']);
        $this->assertEquals($organizationData['name'], $response['data']['name']);
        $this->assertEquals($organizationData['email'], $response['data']['email']);
        $this->assertEquals($organizationData['phone'], $response['data']['phone']);
        $this->assertEquals($organizationData['address'], $response['data']['address']);
        $this->assertNull($response['data']['deletedAt']);
        $this->assertNotNull($response['data']['createdAt']);
        $this->assertNotNull($response['data']['updatedAt']);

        return $response['data']['id'];
    }

    /**
     * @depends testCreateOrganization
     */
    public function testGetOrganization(string $organizationId): void
    {
        $this->client->request('GET', self::BASE_ROUTE . '/' . self::LOCALE . '/organizations/' . $organizationId);

        $this->assertResponseStatusCodeSame(200);
        $response = $this->getDecodedJsonResponse();
        
        $this->assertArrayHasKey('data', $response);
        $this->assertEquals($organizationId, $response['data']['id']);
        $this->assertEquals('Test Organization', $response['data']['name']);
    }

    /**
     * @depends testCreateOrganization
     */
    public function testUpdateOrganization(string $organizationId): void
    {
        $updatedData = [
            'name' => 'Updated Organization',
            'email' => '<EMAIL>',
            'phone' => '******-9999',
            'address' => '456 Updated Street, Updated City, UC 67890'
        ];

        $this->client->jsonRequest(
            'PUT',
            self::BASE_ROUTE . '/' . self::LOCALE . '/organizations/' . $organizationId,
            $updatedData
        );

        $this->assertResponseStatusCodeSame(200);
        $response = $this->getDecodedJsonResponse();
        
        $this->assertArrayHasKey('data', $response);
        $this->assertEquals($organizationId, $response['data']['id']);
        $this->assertEquals($updatedData['name'], $response['data']['name']);
        $this->assertEquals($updatedData['email'], $response['data']['email']);
        $this->assertEquals($updatedData['phone'], $response['data']['phone']);
        $this->assertEquals($updatedData['address'], $response['data']['address']);
    }

    /**
     * @depends testCreateOrganization
     */
    public function testDeleteOrganization(string $organizationId): void
    {
        $this->client->request('DELETE', self::BASE_ROUTE . '/' . self::LOCALE . '/organizations/' . $organizationId);

        $this->assertResponseStatusCodeSame(204);
        
        // Verify organization is soft deleted
        $this->client->request('GET', self::BASE_ROUTE . '/' . self::LOCALE . '/organizations/' . $organizationId);
        $this->assertResponseStatusCodeSame(404);
    }

    public function testListOrganizations(): void
    {
        $this->client->request('GET', self::BASE_ROUTE . '/' . self::LOCALE . '/organizations');

        $this->assertResponseStatusCodeSame(200);
        $response = $this->getDecodedJsonResponse();
        
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('organizations', $response['data']);
        $this->assertArrayHasKey('pagination', $response['data']);
        $this->assertIsArray($response['data']['organizations']);
        $this->assertArrayHasKey('page', $response['data']['pagination']);
        $this->assertArrayHasKey('limit', $response['data']['pagination']);
        $this->assertArrayHasKey('total', $response['data']['pagination']);
        $this->assertArrayHasKey('totalPages', $response['data']['pagination']);
    }

    public function testListOrganizationsWithPagination(): void
    {
        $this->client->request('GET', self::BASE_ROUTE . '/' . self::LOCALE . '/organizations?page=1&limit=5');

        $this->assertResponseStatusCodeSame(200);
        $response = $this->getDecodedJsonResponse();
        
        $this->assertEquals(1, $response['data']['pagination']['page']);
        $this->assertEquals(5, $response['data']['pagination']['limit']);
    }

    public function testCreateOrganizationWithoutPhone(): void
    {
        $organizationData = [
            'name' => 'Test Organization No Phone',
            'email' => '<EMAIL>',
            'address' => '789 No Phone Street, No Phone City, NP 11111'
        ];

        $this->makeRequest(pathParams: ['_locale' => self::LOCALE], data: $organizationData);

        $this->assertResponseStatusCodeSame(201);
        $response = $this->getDecodedJsonResponse();
        
        $this->assertArrayHasKey('data', $response);
        $this->assertEquals($organizationData['name'], $response['data']['name']);
        $this->assertNull($response['data']['phone']);
    }

    protected function getRoute(): string
    {
        return '/organizations';
    }
}
