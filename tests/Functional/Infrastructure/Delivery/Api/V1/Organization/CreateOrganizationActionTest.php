<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1\Organization;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Shared\Factory\DateTimeFactory;
use App\Tests\Shared\Random\Generator;
use Exception;
use Symfony\Component\Messenger\Transport\InMemoryTransport;

final class CreateOrganizationActionTest extends FunctionalTestCase
{
    protected const string ROUTE = '/tenants/v1/{_locale}/organization';
    protected const string METHOD = 'POST';
    protected const string LOCALE = 'en';

    /**
     * @dataProvider provideCreateOrganizationValidData
     */
    public function testCreateOrganizationWorks(array $data, array $response): void
    {
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE);
        $this->client->jsonRequest(method: self::METHOD, uri: $route, parameters: $data);
        $this->assertResponseStatusCodeSame(201);
        $this->assertMatchesPattern(['data' => $response], $this->getDecodedJsonResponse());
    }

    public function testCreateCompanyFailsWithExistingName(): void
    {
        $data = [
            'name' => OrganizationFactory::NAME,
            'address' => OrganizationFactory::ADDRESS,
            'email' => OrganizationFactory::EMAIL,
            'phone' => OrganizationFactory::PHONE,
        ];
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE);
        $this->client->jsonRequest(self::METHOD, $route, $data);
        $this->assertResponseStatusCodeSame(422);

        $expectedResponse = [
            'errors' => [
                'name' => sprintf('Name %s already exists.', OrganizationFactory::NAME),
            ],
        ];
        $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());
    }

    /**
     * @dataProvider provideCreateOrganizationInvalidData
     */
    public function testCreateCompanyFailsWithInvalidData(array $data, array $errors): void
    {
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE);
        $this->client->jsonRequest(self::METHOD, $route, $data);

        $this->assertResponseStatusCodeSame(400);
        $expectedResponse = ['errors' => $errors];
        $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());
    }

    public function provideCreateOrganizationValidData(): array
    {
        return [
            'validData' => [
                [
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'vatNumber' => OrganizationFactory::VAT_NUMBER,
                    'email' => OrganizationFactory::EMAIL,
                    'phone' => OrganizationFactory::PHONE,
                ],
                [
                    'id' => '@uuid@',
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'vatNumber' => OrganizationFactory::VAT_NUMBER,
                    'email' => OrganizationFactory::EMAIL,
                    'phone' => OrganizationFactory::PHONE,
                    'isSelfManaged' => OrganizationFactory::SELF_MANAGED,
                    'companyNumber' => OrganizationFactory::COMPANY_NUMBER,
                    'companyAddress' => OrganizationFactory::COMPANY_ADDRESS,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'shopDomains' => '@array@',
                ],
            ],
            'allAllowedCharactersMail' => [
                [
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'vatNumber' => OrganizationFactory::VAT_NUMBER,
                    'email' => OrganizationFactory::NON_EXISTING_EMAIL_ALL_ALLOWED_CHARACTERS,
                    'phone' => OrganizationFactory::PHONE,
                    'isSelfManaged' => OrganizationFactory::SELF_MANAGED,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                ],
                [
                    'id' => '@uuid@',
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'vatNumber' => OrganizationFactory::VAT_NUMBER,
                    'email' => OrganizationFactory::NON_EXISTING_EMAIL_ALL_ALLOWED_CHARACTERS,
                    'phone' => OrganizationFactory::PHONE,
                    'isSelfManaged' => OrganizationFactory::SELF_MANAGED,
                    'companyNumber' => null,
                    'companyAddress' => null,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'shopDomains' => '@array@',
                ],
            ],
            'nullablePhoneNumber' => [
                [
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'email' => OrganizationFactory::EMAIL,
                    'phone' => null,
                    'isSelfManaged' => OrganizationFactory::SELF_MANAGED,
                    'companyNumber' => null,
                    'companyAddress' => null,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                ],
                [
                    'id' => '@uuid@',
                    'name' => OrganizationFactory::NON_EXISTING_NAME,
                    'vatNumber' => null,
                    'email' => OrganizationFactory::EMAIL,
                    'phone' => OrganizationFactory::PHONE,
                    'isSelfManaged' => OrganizationFactory::SELF_MANAGED,
                    'companyNumber' => null,
                    'companyAddress' => null,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'shopDomains' => '@array@',
                ],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function provideCreateOrganizationInvalidData(): array
    {
        return [
            'emptyBody' => [
                [],
                [
                    'name' => 'This value should not be blank.',
                    'email' => 'This value should not be blank.',
                    'address' => 'This value should not be blank.',
                ],
            ],
            'emptyName' => [
                ['address' => 'sample address', 'email' => '<EMAIL>', 'phone' => '************'],
                ['name' => 'This value should not be blank.'],
            ],
            'emptyEmail' => [
                ['name' => 'Valid Name', 'address' => 'sample address', 'phone' => '(*************'],
                ['email' => 'This value should not be blank.'],
            ],
            'invalidEmail' => [
                ['name' => 'Valid Name', 'address' => 'sample address', 'email' => 'invalidEmail', 'phone' => '+************'],
                ['email' => 'This value is not a valid email address.'],
            ],
            'emptyPhone' => [
                ['name' => 'Valid Name', 'address' => 'sample address', 'email' => '<EMAIL>'],
                ['phone' => 'This value should not be blank.'],
            ],
            'longerName' => [
                [
                    'name' => Generator::string(101),
                    'address' => 'sample address',
                    'email' => '<EMAIL>',
                    'phone' => '**************',
                ],
                ['name' => 'This value is too long. It should have 100 characters or less.'],
            ],
            'shortPhone' => [
                [
                    'name' => 'Valid Name',
                    'address' => 'sample address',
                    'email' => '<EMAIL>',
                    'phone' => '12',
                ],
                ['phone' => 'This value is too short. It should have 3 characters or more.'],
            ],
            'longerPhone' => [
                [
                    'name' => 'Valid Name',
                    'address' => 'sample address',
                    'email' => '<EMAIL>',
                    'phone' => '(+359) 123 456 789 123 456 789 123 456 789 123 456 789',
                ],
                ['phone' => 'This value is too long. It should have 30 characters or less.'],
            ],
            'invalidPhone' => [
                [
                    'name' => 'Valid Name',
                    'address' => 'sample address',
                    'email' => '<EMAIL>',
                    'phone' => '0883_5402a010',
                ],
                ['phone' => 'Invalid phone number.'],
            ],
        ];
    }
}
