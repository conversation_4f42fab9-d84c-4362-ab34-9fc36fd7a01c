<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1\Admin\Company;

use App\Domain\Model\Company\CompanyCreated;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Shared\Factory\CompanyFactory;
use App\Tests\Shared\Factory\DateTimeFactory;
use App\Tests\Shared\Random\Generator;
use Exception;
use Symfony\Component\Messenger\Transport\InMemoryTransport;

final class CreateCompanyActionTest extends FunctionalTestCase
{
    protected const ROUTE = '/tenants/admin/v1/{_locale}/companies';
    protected const METHOD = 'POST';

    /**
     * @dataProvider provideCreateCompanyValidData
     */
    public function testCreateCompanyWorks(array $data, array $response): void
    {
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE);
        $this->client->jsonRequest(method: self::METHOD, uri: $route, parameters: $data);
        $this->assertResponseStatusCodeSame(201);
        $this->assertMatchesPattern(['data' => $response], $this->getDecodedJsonResponse());

        /** @var InMemoryTransport $transport */
        $transport = self::getContainer()->get('messenger.transport.async_created_company');
        $this->assertCount(1, $transport->getSent());
        $this->assertInstanceOf(CompanyCreated::class, $transport->getSent()[0]->getMessage());
    }

    public function testCreateCompanyFailsWithExistingName(): void
    {
        $data = [
            'name' => CompanyFactory::NAME,
            'vatNumber' => CompanyFactory::VAT_NUMBER,
            'email' => CompanyFactory::EMAIL,
            'phone' => CompanyFactory::PHONE,
            'isSelfManaged' => CompanyFactory::SELF_MANAGED,
            'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
            'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
        ];
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE);
        $this->client->jsonRequest(self::METHOD, $route, $data);
        $this->assertResponseStatusCodeSame(422);

        $expectedResponse = [
            'errors' => [
                'name' => sprintf('Name %s already exists.', CompanyFactory::NAME),
            ],
        ];
        $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());

        /** @var InMemoryTransport $transport */
        $transport = self::getContainer()->get('messenger.transport.async_created_company');
        $this->assertCount(0, $transport->getSent());
    }

    /**
     * @dataProvider provideCreateCompanyInvalidData
     */
    public function testCreateCompanyFailsWithInvalidData(array $data, array $errors): void
    {
        $route = str_replace('{_locale}', self::LOCALE, self::ROUTE);
        $this->client->jsonRequest(self::METHOD, $route, $data);

        $this->assertResponseStatusCodeSame(400);
        $expectedResponse = ['errors' => $errors];
        $this->assertMatchesPattern($expectedResponse, $this->getDecodedJsonResponse());

        /** @var InMemoryTransport $transport */
        $transport = self::getContainer()->get('messenger.transport.async_created_company');
        $this->assertCount(0, $transport->getSent());
    }

    public function provideCreateCompanyValidData(): array
    {
        return [
            'validData' => [
                [
                    'name' => CompanyFactory::NON_EXISTING_NAME,
                    'vatNumber' => CompanyFactory::VAT_NUMBER,
                    'email' => CompanyFactory::EMAIL,
                    'phone' => CompanyFactory::PHONE,
                    'isSelfManaged' => CompanyFactory::SELF_MANAGED,
                    'companyNumber' => CompanyFactory::COMPANY_NUMBER,
                    'companyAddress' => CompanyFactory::COMPANY_ADDRESS,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                ],
                [
                    'id' => '@uuid@',
                    'name' => CompanyFactory::NON_EXISTING_NAME,
                    'vatNumber' => CompanyFactory::VAT_NUMBER,
                    'email' => CompanyFactory::EMAIL,
                    'phone' => CompanyFactory::PHONE,
                    'isSelfManaged' => CompanyFactory::SELF_MANAGED,
                    'companyNumber' => CompanyFactory::COMPANY_NUMBER,
                    'companyAddress' => CompanyFactory::COMPANY_ADDRESS,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'shopDomains' => '@array@',
                ],
            ],
            'cyrillicMail' => [
                [
                    'name' => CompanyFactory::NON_EXISTING_NAME,
                    'vatNumber' => CompanyFactory::VAT_NUMBER,
                    'email' => CompanyFactory::NON_EXISTING_EMAIL_CYRILLIC,
                    'phone' => CompanyFactory::PHONE,
                    'isSelfManaged' => CompanyFactory::SELF_MANAGED,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                ],
                [
                    'id' => '@uuid@',
                    'name' => CompanyFactory::NON_EXISTING_NAME,
                    'vatNumber' => CompanyFactory::VAT_NUMBER,
                    'email' => CompanyFactory::NON_EXISTING_EMAIL_CYRILLIC,
                    'phone' => CompanyFactory::PHONE,
                    'isSelfManaged' => CompanyFactory::SELF_MANAGED,
                    'companyNumber' => null,
                    'companyAddress' => null,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'shopDomains' => '@array@',
                ],
            ],
            'allAllowedCharactersMail' => [
                [
                    'name' => CompanyFactory::NON_EXISTING_NAME,
                    'vatNumber' => CompanyFactory::VAT_NUMBER,
                    'email' => CompanyFactory::NON_EXISTING_EMAIL_ALL_ALLOWED_CHARACTERS,
                    'phone' => CompanyFactory::PHONE,
                    'isSelfManaged' => CompanyFactory::SELF_MANAGED,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                ],
                [
                    'id' => '@uuid@',
                    'name' => CompanyFactory::NON_EXISTING_NAME,
                    'vatNumber' => CompanyFactory::VAT_NUMBER,
                    'email' => CompanyFactory::NON_EXISTING_EMAIL_ALL_ALLOWED_CHARACTERS,
                    'phone' => CompanyFactory::PHONE,
                    'isSelfManaged' => CompanyFactory::SELF_MANAGED,
                    'companyNumber' => null,
                    'companyAddress' => null,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'shopDomains' => '@array@',
                ],
            ],
            'nullableVatNumber' => [
                [
                    'name' => CompanyFactory::NON_EXISTING_NAME,
                    'vatNumber' => null,
                    'email' => CompanyFactory::EMAIL,
                    'phone' => CompanyFactory::PHONE,
                    'isSelfManaged' => CompanyFactory::SELF_MANAGED,
                    'companyNumber' => null,
                    'companyAddress' => null,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                ],
                [
                    'id' => '@uuid@',
                    'name' => CompanyFactory::NON_EXISTING_NAME,
                    'vatNumber' => null,
                    'email' => CompanyFactory::EMAIL,
                    'phone' => CompanyFactory::PHONE,
                    'isSelfManaged' => CompanyFactory::SELF_MANAGED,
                    'companyNumber' => null,
                    'companyAddress' => null,
                    'createdAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'updatedAt' => '@datetime@.isInDateFormat("'.DateTimeFactory::DATE_TIME_FORMAT.'")',
                    'shopDomains' => '@array@',
                ],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function provideCreateCompanyInvalidData(): array
    {
        return [
            'emptyBody' => [
                [],
                [
                    'name' => 'This value should not be blank.',
                    'email' => 'This value should not be blank.',
                    'phone' => 'This value should not be blank.',
                ],
            ],
            'emptyName' => [
                ['vatNumber' => 'validVatNumber', 'email' => '<EMAIL>', 'phone' => '************'],
                ['name' => 'This value should not be blank.'],
            ],
            'emptyEmail' => [
                ['name' => 'Valid Name', 'vatNumber' => 'validVatNumber', 'phone' => '(*************'],
                ['email' => 'This value should not be blank.'],
            ],
            'invalidEmail' => [
                ['name' => 'Valid Name', 'vatNumber' => 'validVatNumber', 'email' => 'invalidEmail', 'phone' => '+************'],
                ['email' => 'This value is not a valid email address.'],
            ],
            'emptyPhone' => [
                ['name' => 'Valid Name', 'vatNumber' => 'validVatNumber', 'email' => '<EMAIL>'],
                ['phone' => 'This value should not be blank.'],
            ],
            'longerName' => [
                [
                    'name' => Generator::string(101),
                    'vatNumber' => 'validVatNumber',
                    'email' => '<EMAIL>',
                    'phone' => '**************',
                ],
                ['name' => 'This value is too long. It should have 100 characters or less.'],
            ],
            'longerVat' => [
                [
                    'name' => 'Valid Name',
                    'vatNumber' => Generator::string(260),
                    'email' => '<EMAIL>',
                    'phone' => '(+359) 0888 888 888',
                ],
                ['vatNumber' => 'This value is too long. It should have 255 characters or less.'],
            ],
            'shortPhone' => [
                [
                    'name' => 'Valid Name',
                    'vatNumber' => 'validVatNumber',
                    'email' => '<EMAIL>',
                    'phone' => '12',
                ],
                ['phone' => 'This value is too short. It should have 3 characters or more.'],
            ],
            'longerPhone' => [
                [
                    'name' => 'Valid Name',
                    'vatNumber' => 'validVatNumber',
                    'email' => '<EMAIL>',
                    'phone' => '(+359) 123 456 789 123 456 789 123 456 789 123 456 789',
                ],
                ['phone' => 'This value is too long. It should have 30 characters or less.'],
            ],
            'invalidPhone' => [
                [
                    'name' => 'Valid Name',
                    'vatNumber' => 'validVatNumber',
                    'email' => '<EMAIL>',
                    'phone' => '0883_5402a010',
                ],
                ['phone' => 'Invalid phone number.'],
            ],
        ];
    }
}
