<?php

declare(strict_types=1);

namespace App\Tests\Shared\Factory;

use App\Domain\Model\Organization\Organization;

class OrganizationFactory
{
    public const DEFAULT_NAME = 'Test Organization';
    public const DEFAULT_EMAIL = '<EMAIL>';
    public const DEFAULT_PHONE = '+1234567890';
    public const DEFAULT_ADDRESS = '123 Test St, Test City';

    public static function create(array $overrides = []): Organization
    {
        return new Organization(
            $overrides['name'] ?? self::DEFAULT_NAME,
            $overrides['email'] ?? self::DEFAULT_EMAIL,
            $overrides['phone'] ?? self::DEFAULT_PHONE,
            $overrides['address'] ?? self::DEFAULT_ADDRESS,
            $overrides['id'] ?? null
        );
    }
}
