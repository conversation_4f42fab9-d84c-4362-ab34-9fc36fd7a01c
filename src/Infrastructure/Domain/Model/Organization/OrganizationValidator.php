
<?php

declare(strict_types=1);

namespace App\Infrastructure\Domain\Model\Organization;

use App\Domain\Model\Error\ConstraintViolation;
use App\Domain\Model\Error\ConstraintViolationList;
use App\Domain\Model\Organization\OrganizationUniqueNameSpecificationInterface;
use App\Domain\Model\Organization\OrganizationValidatorInterface;
use App\Infrastructure\Domain\Model\AbstractValidator;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Validator\Constraints as Assert;

class OrganizationValidator extends AbstractValidator implements OrganizationValidatorInterface
{
    private readonly TranslatorInterface $translator;
    private readonly OrganizationUniqueNameSpecificationInterface $nameSpecification;

    public function __construct(
        ValidatorInterface $validator,
        TranslatorInterface $translator,
        OrganizationUniqueNameSpecificationInterface $nameSpecification,
    ) {
        parent::__construct($validator);

        $this->translator = $translator;
        $this->nameSpecification = $nameSpecification;
    }

    public function validateNameUniqueness(string $name): ?ConstraintViolation
    {
        if (!$this->nameSpecification->isSatisfiedBy($name)) {
            return new ConstraintViolation(
                $this->translator->trans('Name {name} already exists', ['{name}' => $name]),
                'name',
            );
        }

        return null;
    }

    /**
     * @param array<string, mixed> $data
     */
    public function validateFilters(array $data): ConstraintViolationList
    {
        $fields = [
            'name' => [
                new Assert\Type(['type' => 'string']),
                new Assert\NotBlank(),
            ],
            'email' => [
                new Assert\Type(['type' => 'string']),
                new Assert\NotBlank(),
            ],
        ];

        $errors = $this->validator->validate($data, new Assert\Collection(fields: $fields, allowMissingFields: true));

        return $this->formatErrors($errors);
    }

    protected function getFields(?string $group = null): array
    {
        if (self::GROUP_CREATE === $group) {
            return $this->getCreateFields();
        }

        return $this->getDefaultFields();
    }

    protected function getDefaultFields(): array
    {
        return [
            'name' => [
                new Assert\NotBlank(),
                new Assert\Length(max: 100),
            ],
            'vatNumber' => [
                new Assert\Length(max: 255),
            ],
            'email' => [
                new Assert\NotBlank(),
                new Assert\Email(),
            ],
            'phone' => [
                new Assert\NotBlank(),
                new Assert\Regex([
                    'pattern' => '/^[(]?[+]?[0-9\s()-]*[0-9]{1,}[\s()-]*$/',
                    'message' => $this->translator->trans('Invalid phone number'),
                ]),
                new Assert\Length(min: 3, max: 30),
            ],
            'companyNumber' => new Assert\Optional([
                new Assert\Type(['type' => 'string']),
                new Assert\Length(max: 255),
            ]),
            'companyAddress' => new Assert\Optional([
                new Assert\Type(['type' => 'string']),
                new Assert\Length(max: 255),
            ]),
        ];
    }

    protected function getCreateFields(): array
    {
        return $this->getDefaultFields();
    }
}
