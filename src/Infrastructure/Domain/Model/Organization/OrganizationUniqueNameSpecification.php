<?php

declare(strict_types=1);

namespace App\Infrastructure\Domain\Model\Organization;

use App\Domain\Model\Company\CompanyRepositoryInterface;
use App\Domain\Model\Organization\OrganizationUniqueNameSpecificationInterface;
use Doctrine\ORM\EntityManagerInterface;

class OrganizationUniqueNameSpecification implements OrganizationUniqueNameSpecificationInterface
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly CompanyRepositoryInterface $companyRepository
    ) {
    }

    public function isSatisfiedBy(string $name): bool
    {
        $this->em->getFilters()->disable('softdeleteable');

        return null === $this->companyRepository->findByName($name);
    }
}
