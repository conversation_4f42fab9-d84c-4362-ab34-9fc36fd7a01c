<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Organization;

use OpenApi\Attributes as OA;
use App\Application\Command\Organization\Create\CreateOrganizationCommand;
use App\Application\Query\Organization\Get\GetOrganizationQuery;
use App\Domain\Model\Bus\Command\CommandBusInterface;
use App\Domain\Model\Bus\Query\QueryBusInterface;
use App\Infrastructure\Http\RequestMapper;
use App\Infrastructure\Http\ResponseMapper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[OA\Post(
    path: '/tenants/v1/{_locale}/organizations',
    summary: 'Create Organization',
    security: [['bearerAuth' => []]],
    requestBody: new OA\RequestBody(
        description: 'Organization data',
        required: true,
        content: new OA\JsonContent(
            required: ['name', 'email', 'address'],
            properties: [
                new OA\Property(property: 'name', description: 'Organization name', type: 'string', maxLength: 100, minLength: 3),
                new OA\Property(property: 'email', description: 'Valid email address', type: 'string', format: 'email'),
                new OA\Property(property: 'phone', description: 'Phone number in international format', type: 'string', nullable: true),
                new OA\Property(property: 'address', description: 'Full address as plain text', type: 'string'),
            ],
            example: [
                'name' => 'ACME Holdings Ltd',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'address' => '123 Business Ave, New York, NY 10001, US'
            ]
        )
    ),
    tags: ['Organizations'],
    parameters: [
        new OA\Parameter(ref: '#/components/parameters/_locale'),
    ],
    responses: [
        new OA\Response(
            response: Response::HTTP_CREATED,
            description: 'Organization created successfully',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'data', ref: '#/components/schemas/OrganizationResponse')
                ]
            )
        ),
        new OA\Response(
            response: Response::HTTP_BAD_REQUEST,
            description: 'Validation error'
        )
    ]
)]
class CreateOrganizationAction extends AbstractController
{
    public function __construct(
        private readonly CommandBusInterface $commandBus,
        private readonly RequestMapper       $requestMapper,
        private readonly ResponseMapper      $responseMapper
    ) {}

    /**
     * @throws \ReflectionException
     */
    #[Route('/organizations', name: 'app_create_organization', methods: ['POST'])]
    public function __invoke(Request $request): JsonResponse
    {
        /** @var CreateOrganizationCommand $command */
        $command = $this->requestMapper->fromRequest($request, CreateOrganizationCommand::class);

        return $this->responseMapper->serializeResponse($this->commandBus->dispatch($command), Response::HTTP_CREATED);
    }
}
